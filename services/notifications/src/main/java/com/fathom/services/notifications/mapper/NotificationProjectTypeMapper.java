package com.fathom.services.notifications.mapper;

import com.fathom.services.notifications.configuration.ProjectTypeColorConfiguration;
import com.fathom.services.notifications.model.NotificationProjectsConfiguration;
import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationCreateUpdateDto;
import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationDto;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class NotificationProjectTypeMapper {
  @Autowired private ProjectTypeColorConfiguration projectTypeColorConfiguration;

  @Mapping(target = "organizationId", source = "organizationId")
  public abstract NotificationProjectsConfiguration toEntityUsingDefaultValues(UUID organizationId);

  @Mapping(target = "organizationId", source = "organizationId")
  public abstract NotificationProjectsConfiguration toNewEntity(
      UUID organizationId, NotificationProjectsConfigurationCreateUpdateDto source);

  @Mapping(target = "organizationId", source = "organizationId")
  public abstract NotificationProjectsConfigurationDto toDto(
      NotificationProjectsConfiguration source);

  public abstract NotificationProjectsConfiguration updateDtoToEntity(
      @MappingTarget NotificationProjectsConfiguration notificationProjectsConfiguration,
      NotificationProjectsConfigurationCreateUpdateDto source);

  @AfterMapping
  void afterMapping(@MappingTarget NotificationProjectsConfiguration source) {
    Map<String, String> stringMap = new HashMap<>();

    for (var projectTypeColor : projectTypeColorConfiguration.getProjectTypeColor()) {
      stringMap.put(projectTypeColor.getProjectType(), projectTypeColor.getColor());
    }

    source.setSupportedProjectTypes(stringMap);
  }
}
