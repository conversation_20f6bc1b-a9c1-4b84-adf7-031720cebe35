#!/bin/bash

# Gateway Service Health Endpoint Tests
# This script tests the Spring Boot Actuator health endpoints for the gateway service

set -e

# Configuration
GATEWAY_HOST="localhost"
GATEWAY_PORT="8052"
BASE_URL="http://${GATEWAY_HOST}:${GATEWAY_PORT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
    esac
}

# Function to test an endpoint
test_endpoint() {
    local endpoint=$1
    local expected_status=${2:-200}
    local description=$3
    
    print_status "INFO" "Testing: $description"
    print_status "INFO" "URL: ${BASE_URL}${endpoint}"
    
    # Make the request and capture response
    response=$(curl -s -w "\n%{http_code}" "${BASE_URL}${endpoint}" 2>/dev/null || echo -e "\nERROR")
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    
    # Extract response body (all but last line)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "ERROR" ]; then
        print_status "ERROR" "Failed to connect to ${BASE_URL}${endpoint}"
        print_status "ERROR" "Make sure the gateway service is running on port ${GATEWAY_PORT}"
        return 1
    elif [ "$status_code" = "$expected_status" ]; then
        print_status "SUCCESS" "HTTP $status_code - $description"
        echo -e "${GREEN}Response:${NC}"
        echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
        echo ""
        return 0
    else
        print_status "ERROR" "Expected HTTP $expected_status but got HTTP $status_code"
        echo -e "${RED}Response:${NC}"
        echo "$response_body"
        echo ""
        return 1
    fi
}

# Function to check if service is running
check_service_running() {
    print_status "INFO" "Checking if gateway service is running..."
    
    if curl -s --connect-timeout 5 "${BASE_URL}/actuator/health" > /dev/null 2>&1; then
        print_status "SUCCESS" "Gateway service is running on ${BASE_URL}"
        return 0
    else
        print_status "ERROR" "Gateway service is not responding on ${BASE_URL}"
        print_status "INFO" "To start the gateway service, run:"
        echo "  cd services/gateway"
        echo "  export JWK_SET_URI=\"http://localhost:56001/auth/realms/realm/protocol/openid-connect/certs\""
        echo "  export SPRING_PROFILES_ACTIVE=\"test\""
        echo "  mvn spring-boot:run"
        return 1
    fi
}

# Main test execution
main() {
    echo "=================================================="
    echo "Gateway Service Health Endpoint Tests"
    echo "=================================================="
    echo ""
    
    # Check if jq is available for JSON formatting
    if ! command -v jq &> /dev/null; then
        print_status "WARNING" "jq not found. JSON responses will not be formatted."
        print_status "INFO" "Install jq for better output: sudo apt-get install jq (Ubuntu) or brew install jq (macOS)"
        echo ""
    fi
    
    # Check if service is running
    if ! check_service_running; then
        exit 1
    fi
    
    echo ""
    print_status "INFO" "Starting health endpoint tests..."
    echo ""
    
    # Test basic health endpoint
    test_endpoint "/actuator/health" 200 "Basic Health Check"
    
    # Test detailed health endpoint
    test_endpoint "/actuator/health/readiness" 200 "Readiness Probe"
    
    # Test liveness endpoint
    test_endpoint "/actuator/health/liveness" 200 "Liveness Probe"
    
    # Test info endpoint
    test_endpoint "/actuator/info" 200 "Application Info"
    
    # Test metrics endpoint
    test_endpoint "/actuator/metrics" 200 "Metrics Endpoint"
    
    # Test gateway routes endpoint (specific to Spring Cloud Gateway)
    test_endpoint "/actuator/gateway/routes" 200 "Gateway Routes"
    
    # Test all actuator endpoints
    test_endpoint "/actuator" 200 "Actuator Discovery"
    
    echo ""
    echo "=================================================="
    print_status "SUCCESS" "All health endpoint tests completed!"
    echo "=================================================="
}

# Run the tests
main "$@"
