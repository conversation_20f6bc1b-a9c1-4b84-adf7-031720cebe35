package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import com.fathom.services.apigateway.authorization.preauthorization.frn.ExtractedVariable;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;
import java.util.Optional;

class HeaderVariableExtractor implements ResourceVariableExtractor {

  @Override
  public ExtractedVariable extractVariable(
      ProxiedRequest proxiedRequest, ResourceVariableDefinitionDto resourceVariable) {
    String value = resourceVariable.getValue();
    // case insensitive header
    String headerValue =
        Optional.ofNullable(proxiedRequest.getHeaders().getFirst(value.toLowerCase()))
            .orElseGet(() -> proxiedRequest.getHeaders().getFirst(value));
    if (headerValue == null) {
      throw new ResourceVariableDefinitionException(resourceVariable);
    }
    return new ExtractedVariable(resourceVariable.getVariable(), headerValue);
  }
}
