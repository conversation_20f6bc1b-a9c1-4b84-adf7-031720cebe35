package com.fathom.services.apigateway.util;

import static com.fathom.services.apigateway.BaseIntegrationTest.MAPPER;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Objects;
import lombok.experimental.UtilityClass;

@UtilityClass
public class JsonUtil {

  public String loadFromFile(String fileName) {
    try {
      URL resource = JsonUtil.class.getClassLoader().getResource(fileName);
      File file = new File(Objects.requireNonNull(resource).getFile());
      return MAPPER.readTree(file).toString();
    } catch (IOException e) {
      throw new RuntimeException("Unable to load file", e);
    }
  }
}
