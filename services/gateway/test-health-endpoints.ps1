# Gateway Service Health Endpoint Tests
# PowerShell script to test Spring Boot Actuator endpoints

param(
    [string]$Host = "localhost",
    [int]$Port = 8052,
    [string]$AuthToken = ""
)

$BaseUrl = "http://${Host}:${Port}"

# Function to test an endpoint
function Test-Endpoint {
    param(
        [string]$Endpoint,
        [string]$Description,
        [int]$ExpectedStatus = 200
    )
    
    $Url = "${BaseUrl}${Endpoint}"
    Write-Host "`n=== Testing: $Description ===" -ForegroundColor Cyan
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $Headers = @{}
        if ($AuthToken) {
            $Headers["Authorization"] = "Bearer $AuthToken"
        }
        
        $Response = Invoke-WebRequest -Uri $Url -UseBasicParsing -Headers $Headers -ErrorAction Stop
        
        if ($Response.StatusCode -eq $ExpectedStatus) {
            Write-Host "✅ SUCCESS: HTTP $($Response.StatusCode)" -ForegroundColor Green
            Write-Host "Response:" -ForegroundColor Yellow
            
            # Try to format JSON if possible
            try {
                $JsonResponse = $Response.Content | ConvertFrom-Json | ConvertTo-Json -Depth 10
                Write-Host $JsonResponse -ForegroundColor White
            } catch {
                Write-Host $Response.Content -ForegroundColor White
            }
        } else {
            Write-Host "⚠️  UNEXPECTED: HTTP $($Response.StatusCode) (expected $ExpectedStatus)" -ForegroundColor Yellow
            Write-Host $Response.Content -ForegroundColor White
        }
    }
    catch {
        $StatusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "❌ ERROR: HTTP $StatusCode" -ForegroundColor Red
        
        if ($StatusCode -eq 401) {
            Write-Host "Authentication required. The endpoint is protected." -ForegroundColor Yellow
            Write-Host "Try running with -AuthToken parameter if you have a JWT token." -ForegroundColor Yellow
        }
        
        Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to check if service is running
function Test-ServiceRunning {
    Write-Host "🔍 Checking if gateway service is running on ${BaseUrl}..." -ForegroundColor Cyan
    
    try {
        $Response = Invoke-WebRequest -Uri "${BaseUrl}/actuator/health" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        Write-Host "✅ Service is responding" -ForegroundColor Green
        return $true
    }
    catch {
        $StatusCode = $_.Exception.Response.StatusCode.value__
        if ($StatusCode -eq 401) {
            Write-Host "✅ Service is running (but endpoints are protected)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Service is not responding on ${BaseUrl}" -ForegroundColor Red
            Write-Host "Make sure the gateway service is running:" -ForegroundColor Yellow
            Write-Host "  cd services/gateway" -ForegroundColor Gray
            Write-Host "  `$env:JWK_SET_URI=`"https://auth.fthm.io/realms/FathomDevRealm/protocol/openid-connect/certs`"" -ForegroundColor Gray
            Write-Host "  `$env:SPRING_PROFILES_ACTIVE=`"test`"" -ForegroundColor Gray
            Write-Host "  mvn spring-boot:run" -ForegroundColor Gray
            return $false
        }
    }
}

# Main execution
Write-Host "================================================" -ForegroundColor Magenta
Write-Host "Gateway Service Health Endpoint Tests" -ForegroundColor Magenta
Write-Host "================================================" -ForegroundColor Magenta

# Check if service is running
if (-not (Test-ServiceRunning)) {
    exit 1
}

# Test endpoints
Test-Endpoint "/actuator/health" "Basic Health Check"
Test-Endpoint "/actuator/info" "Application Info"
Test-Endpoint "/actuator" "Actuator Discovery"
Test-Endpoint "/actuator/metrics" "Metrics Endpoint"
Test-Endpoint "/actuator/gateway/routes" "Gateway Routes (Spring Cloud Gateway)"

# Test with authentication expectation
Write-Host "`n=== Summary ===" -ForegroundColor Magenta
Write-Host "The gateway service is running on port $Port" -ForegroundColor Green
Write-Host "However, actuator endpoints are currently protected by OAuth2 authentication" -ForegroundColor Yellow
Write-Host "This may be a configuration issue with the allowed-routes security bypass" -ForegroundColor Yellow

Write-Host "`nTo test with authentication, obtain a JWT token and run:" -ForegroundColor Cyan
Write-Host "  .\test-health-endpoints.ps1 -AuthToken `"YOUR_JWT_TOKEN`"" -ForegroundColor Gray
