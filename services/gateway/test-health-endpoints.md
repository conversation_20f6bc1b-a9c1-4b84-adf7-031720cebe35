# Gateway Service Health Endpoint Tests

This document provides curl commands to test the Spring Boot Actuator health endpoints for the gateway service.

## Prerequisites

1. Gateway service must be running on port 8052
2. Start the service with:
   ```bash
   cd services/gateway
   export JWK_SET_URI="https://auth.fthm.io/realms/FathomDevRealm/protocol/openid-connect/certs"
   export SPRING_PROFILES_ACTIVE="test"
   mvn spring-boot:run
   ```

## Current Status

The gateway service is running but the actuator endpoints are protected by OAuth2 authentication, even though they are configured in the `allowed-routes` section.

## Test Commands

### 1. Check if service is running
```bash
# Check if port 8052 is listening
netstat -an | findstr :8052
```

### 2. Test Health Endpoint (Currently requires authentication)
```bash
# Basic health check
curl.exe -v http://localhost:8052/actuator/health

# Expected Response: 401 Unauthorized (currently)
# Should return: 200 OK with health status
```

### 3. Test Info Endpoint (Currently requires authentication)
```bash
# Application info
curl.exe -v http://localhost:8052/actuator/info

# Expected Response: 401 Unauthorized (currently)
# Should return: 200 OK with application info
```

### 4. Test All Actuator Endpoints
```bash
# Discover all actuator endpoints
curl.exe -v http://localhost:8052/actuator

# Expected Response: 401 Unauthorized (currently)
# Should return: 200 OK with list of available endpoints
```

### 5. Test Gateway-Specific Endpoints
```bash
# Gateway routes (Spring Cloud Gateway specific)
curl.exe -v http://localhost:8052/actuator/gateway/routes

# Metrics endpoint
curl.exe -v http://localhost:8052/actuator/metrics
```

## Current Issue

The actuator endpoints are returning `401 Unauthorized` with `WWW-Authenticate: Bearer` header, indicating they require JWT authentication. However, according to the configuration in `application.yml`, these endpoints should be publicly accessible:

```yaml
allowed-routes:
  routes:
  ## actuator (healthcheck)
    - path: "/actuator/**"
      method: "GET"
    - path: "/actuator/health"
      method: "GET"
    - path: "/actuator/info"
      method: "GET"
```

## Troubleshooting

### Check Service Status
```bash
# PowerShell
netstat -an | findstr :8052

# Should show:
# TCP    0.0.0.0:8052           0.0.0.0:0              LISTENING
# TCP    [::]:8052              [::]:0                 LISTENING
```

### Test with Authentication (if needed)
If the endpoints require authentication, you would need to:

1. Get a JWT token from the auth server
2. Include it in the request:
```bash
curl.exe -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:8052/actuator/health
```

## Expected Responses (when working correctly)

### Health Endpoint
```json
{
  "status": "UP",
  "components": {
    "diskSpace": {
      "status": "UP",
      "details": {
        "total": 123456789,
        "free": 987654321,
        "threshold": 10485760,
        "exists": true
      }
    },
    "ping": {
      "status": "UP"
    }
  }
}
```

### Info Endpoint
```json
{
  "app": {
    "name": "api-gateway",
    "version": "0.0.2-SNAPSHOT"
  },
  "diagnostics": {
    "service-name": "diagnostics",
    "version": "0.0.1-dev",
    "build-timestamp": "1970-01-01T00:00:00Z",
    "git-hash": "unknown",
    "git-branch": "main",
    "git-commit-timestamp": "1970-01-01T00:00:00Z"
  }
}
```

## Next Steps

To fix the authentication issue with actuator endpoints, the security configuration needs to be updated to properly handle the `allowed-routes` configuration and bypass authentication for these specific paths.
