package com.fathom.services.modulemanagement.controllers.impl;

import static com.fathom.services.modulemanagement.util.StaticProperties.*;

import com.fathom.services.modulemanagement.controllers.ModuleControllerV1;
import com.fathom.services.modulemanagement.model.dto.ModuleCreateUpdateDto;
import com.fathom.services.modulemanagement.model.dto.ModuleDto;
import com.fathom.services.modulemanagement.model.dto.ModulePageableFilter;
import com.fathom.services.modulemanagement.model.dto.ModuleUpdateManyDto;
import com.fathom.services.modulemanagement.services.ModuleService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@RestController
@RequiredArgsConstructor
public class ModuleController implements ModuleControllerV1 {
  private final ModuleService moduleService;

  @Override
  @GetMapping("modules")
  public ResponseEntity<Page<ModuleDto>> getModulesByOrganizationIdAndProjectId(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @RequestParam(defaultValue = "1", required = false) int pageNumber,
      @RequestParam(defaultValue = "10", required = false) int pageSize,
      @RequestParam(defaultValue = "false", required = false) boolean pageable) {

    return ResponseEntity.ok(
        moduleService.getModulesByOrganizationIdAndProjectId(
            organizationId, projectId, pageNumber, pageSize, pageable));
  }

  @Override
  @GetMapping("modules/{id}")
  public ResponseEntity<ModuleDto> getModuleById(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @PathVariable UUID id) {
    return ResponseEntity.ok(moduleService.getModuleById(organizationId, projectId, id));
  }

  @Override
  @GetMapping("modules/exists-by-name/{name}")
  public ResponseEntity<Boolean> existsByOrganizationIdAndName(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId, @PathVariable String name) {

    return ResponseEntity.ok(moduleService.existsByOrganizationIdAndName(organizationId, name));
  }

  @Override
  @PostMapping("modules")
  public ResponseEntity<ModuleDto> createModule(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @RequestBody @Validated ModuleCreateUpdateDto moduleCreateUpdateDto) {

    return ResponseEntity.ok(
        moduleService.createModule(organizationId, projectId, moduleCreateUpdateDto));
  }

  @Override
  @PutMapping("modules/{id}")
  public ResponseEntity<ModuleDto> updateModule(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @PathVariable UUID id,
      @RequestBody ModuleCreateUpdateDto moduleCreateUpdateDto) {

    return ResponseEntity.ok(
        moduleService.updateModule(organizationId, projectId, id, moduleCreateUpdateDto));
  }

  @Override
  @PutMapping("modules")
  public ResponseEntity<Set<ModuleDto>> updateManyModule(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @RequestBody Set<ModuleUpdateManyDto> moduleUpdateManyDto) {

    return ResponseEntity.ok(
        moduleService.updateManyModule(organizationId, projectId, moduleUpdateManyDto));
  }

  @Override
  @PostMapping("modules/filtered")
  public ResponseEntity<Page<ModuleDto>> getFiltered(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @RequestBody ModulePageableFilter modulePageableFilter) {

    return ResponseEntity.ok(
        moduleService.getFiltered(organizationId, projectId, modulePageableFilter));
  }

  @Override
  @DeleteMapping("modules/{id}")
  public ResponseEntity<ModuleDto> deleteModule(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @PathVariable UUID id) {
    moduleService.deleteModule(organizationId, id);
    return ResponseEntity.ok().build();
  }

  @Override
  @PostMapping("modules/delete")
  public ResponseEntity<ModuleDto> deleteModules(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @RequestBody Set<UUID> ids) {
    moduleService.deleteModules(organizationId, ids);
    return ResponseEntity.ok().build();
  }
}
