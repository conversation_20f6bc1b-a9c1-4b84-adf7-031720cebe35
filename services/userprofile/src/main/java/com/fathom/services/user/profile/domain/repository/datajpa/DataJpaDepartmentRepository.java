package com.fathom.services.user.profile.domain.repository.datajpa;

import com.fathom.services.user.profile.domain.entity.Department;
import com.fathom.services.user.profile.domain.repository.DepartmentRepository;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public class DataJpaDepartmentRepository implements DepartmentRepository {
  @Autowired CrudDeapertmentRepository jpaRepository;

  @Override
  public Department save(Department department) {
    return jpaRepository.save(department);
  }

  @Override
  public Set<Department> saveAll(Set<Department> departments) {
    return new HashSet<>(jpaRepository.saveAll(departments));
  }

  @Override
  public boolean deleteById(Integer id) {
    if (!jpaRepository.existsById(id)) {
      return Boolean.FALSE;
    }
    jpaRepository.deleteById(id);
    return Boolean.TRUE;
  }

  @Override
  public Department findById(Integer id) {
    return jpaRepository.findById(id).orElse(null);
  }

  @Override
  public Page<Department> findAllByOrganizationId(UUID organizationId, Pageable pageable) {
    return jpaRepository.findAllByOrganizationId(organizationId, pageable);
  }

  @Override
  public Set<Department> findAllByIds(Set<Integer> ids) {
    return new HashSet<>(jpaRepository.findAllById(ids));
  }

  @Override
  public Page<Department> findAll(Pageable pageable) {
    return jpaRepository.findAll(pageable);
  }
}
