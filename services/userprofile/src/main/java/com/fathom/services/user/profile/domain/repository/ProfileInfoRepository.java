package com.fathom.services.user.profile.domain.repository;

import com.fathom.services.user.profile.domain.dto.ProfileContactDTO;
import com.fathom.services.user.profile.domain.entity.CompositeId;
import com.fathom.services.user.profile.domain.entity.ProfileInfo;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ProfileInfoRepository extends JpaRepository<ProfileInfo, CompositeId> {

  @Query("SELECT p from ProfileInfo p where p.compositeId.email in :emails")
  List<ProfileInfo> findByCompositeId_EmailIn(@Param("emails") Collection<String> emails);

  @Query("SELECT profile FROM ProfileInfo profile WHERE profile.compositeId.email = :email")
  ProfileInfo findByEmail(@Param("email") String email);

  @Query("SELECT profile FROM ProfileInfo profile WHERE profile.compositeId.email IN :emails")
  List<ProfileInfo> findAllByEmail(@Param("emails") Set<String> emails);

  @Query(
      "SELECT new com.fathom.services.user.profile.domain.dto.ProfileContactDTO( profile.firstName, profile.lastName, profile.compositeId.profileId AS profileId) "
          + " FROM ProfileInfo profile WHERE profile.compositeId.profileId NOT IN :profileIds AND profile.defaultOrganizationId = :defaultOrganizationId")
  List<ProfileContactDTO> findAllContacts(
      @Param("defaultOrganizationId") UUID defaultOrganizationId,
      @Param("profileIds") List<UUID> profileIds);

  @Query(
      "SELECT profile FROM ProfileInfo profile WHERE profile.compositeId.profileId IN :profileIds")
  List<ProfileInfo> findAllByProfileId(@Param("profileIds") Set<UUID> profileIds);
}
