package com.fathom.services.user.profile.controller;

import static com.fathom.services.user.profile.config.constants.SwaggerMessage.ENDPOINT_EMAIL_PARAM;
import static com.fathom.services.user.profile.controller.impl.ProfileInfoControllerImpl.EMAIL_HEADER_NAME;

import com.fathom.services.user.profile.config.constants.SwaggerMessage;
import com.fathom.services.user.profile.domain.dto.DateConfigurationDTO;
import com.fathom.services.user.profile.domain.dto.TimeZoneDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import org.springframework.web.bind.annotation.RequestHeader;

public interface DateConfigurationController {
  @Operation(
      summary = "Get all time zones",
      description = SwaggerMessage.ENDPOINT_CREATE_TIME_ZONE_LIST)
  List<TimeZoneDTO> getAll(@Parameter(description = ENDPOINT_EMAIL_PARAM) String email);

  @Operation(
      summary = "Get time configuration for user",
      description = SwaggerMessage.ENDPOINT_GET_TIME_CONFIGURATION)
  DateConfigurationDTO getTimeConfiguration(@RequestHeader(EMAIL_HEADER_NAME) String email);
}
