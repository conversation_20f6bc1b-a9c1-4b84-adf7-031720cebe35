package com.fathom.services.user.profile;

import com.fathom.diagnostics.configuration.DiagnosticProperties;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

@OpenAPIDefinition(
    info =
        @Info(
            title = "User profile service",
            description = "This application used to manage user profiles, departments, and profile images",
            license = @License(name = "Apache 2.0", url = "https://fathoms.io"),
            contact =
                @Contact(
                    name = "Fathom Solutions",
                    email = "<EMAIL>",
                    url = "https://fathom.io")))
@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties(DiagnosticProperties.class)
@ComponentScan(basePackages = {
        "com.fathom.services.user.profile",
        "com.fathom.diagnostics"
})
public class Application {
  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }
}
