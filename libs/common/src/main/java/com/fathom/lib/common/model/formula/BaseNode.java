package com.fathom.lib.common.model.formula;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = "nodeType",
    visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(name = "Operator", value = OperatorNode.class),
  @JsonSubTypes.Type(name = "Property", value = PropertyNode.class),
  @JsonSubTypes.Type(name = "Constant", value = ConstantNode.class),
  @JsonSubTypes.Type(name = "Result", value = ResultNode.class),
  @JsonSubTypes.Type(name = "TrigonometricFunction", value = TrigonometricFunctionNode.class)
})
public abstract class BaseNode {

  private NodeTypes nodeType;

  @NotNull(groups = {Create.class})
  private Long id;

  @JsonProperty("xAxisPosition")
  @NotNull(groups = {Create.class})
  private Double xAxisPosition;

  @JsonProperty("yAxisPosition")
  @NotNull(groups = {Create.class})
  private Double yAxisPosition;

  public BaseNode(NodeTypes nodeType) {
    this.nodeType = nodeType;
  }
}
